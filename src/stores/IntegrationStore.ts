import {action, makeAutoObservable, runInAction} from 'mobx';
import {RootStore} from '@/stores/RootStore';
import {Connection} from '@/lib/db/schema';

export interface SetupStep {
    id: string;
    title: string;
    description: string;
    completed: boolean;
    externalUrl?: string;
    buttonText?: string;
}

export type ConnectionType = 'oauth' | 'key' | 'prompt' | 'oauth+prompt' | 'key+prompt';

export interface IntegrationProvider {
    id: string;
    name: string;
    logo: string;
    description: string;
    connected: boolean;
    // New fields for enhanced integration flow
    category?: 'payment' | 'analytics' | 'auth' | 'database' | 'notification' | 'storage' | 'api';
    comingSoon?: boolean;
    dependencies?: string[];          // IDs of required integrations
    connectionType?: ConnectionType;  // How this integration connects
    setupGuideUrl?: string;           // External setup guide if needed
    promptTemplate?: string;          // Template for AI prompt
    setupSteps?: SetupStep[];         // Multi-step setup process
}

export interface Database {
    id: string;
    name: string;
    status: string;
}

export interface ProjectLinkState {
    isLinking: boolean;
    error: string | null;
    success: boolean;
}

export class IntegrationStore {
    private rootStore: RootStore;
    connections: Map<string, Connection> = new Map();
    databases: Map<string, Database[]> = new Map();
    loadingConnections: Set<string> = new Set();
    loadingDatabases: Set<string> = new Set();
    currentSelectedProjectId: string | null = null;
    shouldSendMessage: boolean = false;
    
    // Track setup progress for integrations
    setupProgress: Map<string, { stepId: string, completed: boolean }[]> = new Map();
    
    projectLinkState: Map<string, ProjectLinkState> = new Map();

    providers: IntegrationProvider[] = [
        // Supabase Database - Base integration with no dependencies
        {
            id: 'supabase',
            name: 'Supabase Database',
            logo: '/icons/integrations/supabase.png',
            description: 'PostgreSQL database with realtime subscriptions',
            connected: false,
            category: 'database',
            comingSoon: false,
            connectionType: 'oauth',
            dependencies: [],
            promptTemplate: 'Configure Supabase database with the following schema: {schema}'
        },
        // Supabase Auth - Depends on Supabase Database
        {
            id: 'supabase-auth',
            name: 'Supabase Auth',
            logo: '/icons/integrations/supabase.png',
            description: 'Authentication and user management',
            connected: false,
            category: 'auth',
            comingSoon: false,
            connectionType: 'oauth+prompt',
            dependencies: ['supabase'],
            promptTemplate: 'Configure Supabase Auth with the following settings: {settings}'
        },
        // Supabase Storage - Depends on Supabase Database
        {
            id: 'supabase-storage',
            name: 'Supabase Storage',
            logo: '/icons/integrations/supabase.png',
            description: 'Object storage for files and media',
            connected: false,
            category: 'storage',
            comingSoon: false,
            connectionType: 'oauth',
            dependencies: ['supabase']
        },
        // Supabase API - Depends on Supabase Database
        {
            id: 'supabase-api',
            name: 'Supabase API',
            logo: '/icons/integrations/supabase.png',
            description: 'RESTful and GraphQL APIs for your database',
            connected: false,
            category: 'api',
            comingSoon: false,
            connectionType: 'oauth',
            dependencies: ['supabase']
        },
        // Google Auth - Depends on Supabase Auth
        {
            id: 'google-auth',
            name: 'Google Auth',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/google.png',
            description: 'Authentication with Google accounts',
            connected: false,
            category: 'auth',
            comingSoon: true,
            connectionType: 'oauth+prompt',
            dependencies: ['supabase-auth'],
            setupGuideUrl: 'https://supabase.com/docs/guides/auth/social-login/auth-google',
            promptTemplate: 'Configure Google Auth with the following settings: {settings}',
            setupSteps: [
                {
                    id: 'create-project',
                    title: 'Create Google Cloud Project',
                    description: 'Create a new project in Google Cloud Console',
                    completed: false,
                    externalUrl: 'https://console.cloud.google.com/projectcreate'
                },
                {
                    id: 'enable-api',
                    title: 'Enable Google Identity Platform',
                    description: 'Enable the Identity Platform API',
                    completed: false,
                    externalUrl: 'https://console.cloud.google.com/apis/library/identitytoolkit.googleapis.com'
                },
                {
                    id: 'create-credentials',
                    title: 'Create OAuth Credentials',
                    description: 'Create OAuth client ID and secret',
                    completed: false,
                    externalUrl: 'https://console.cloud.google.com/apis/credentials'
                }
            ]
        },
        // Apple Auth - Depends on Supabase Auth
        {
            id: 'apple-auth',
            name: 'Apple Auth',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/apple.png',
            description: 'Sign in with Apple authentication',
            connected: false,
            category: 'auth',
            comingSoon: true,
            connectionType: 'key+prompt',
            dependencies: ['supabase-auth'],
            setupGuideUrl: 'https://supabase.com/docs/guides/auth/social-login/auth-apple'
        },
        // Stripe - Payment integration
        {
            id: 'stripe',
            name: 'Stripe',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/stripe.png',
            description: 'Payment processing platform for internet businesses',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'key+prompt',
            dependencies: [],
            setupGuideUrl: 'https://stripe.com/docs/development'
        },
        // PostHog - Analytics integration
        {
            id: 'posthog',
            name: 'PostHog',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/posthog.png',
            description: 'Open-source product analytics platform',
            connected: false,
            category: 'analytics',
            comingSoon: true,
            connectionType: 'key',
            dependencies: []
        },
        // RevenueCat - In-app subscription management
        {
            id: 'revenuecat',
            name: 'RevenueCat',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/revenue-cat.png',
            description: 'In-app subscription infrastructure for mobile apps',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'key',
            dependencies: []
        },
        // Apple In-App Purchases
        {
            id: 'apple-iap',
            name: 'Apple In-App Purchases',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/apple.png',
            description: 'In-app purchases and subscriptions for iOS apps',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'key+prompt',
            dependencies: []
        },
        // Google Play Billing
        {
            id: 'google-iap',
            name: 'Google Play Billing',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/google.png',
            description: 'In-app purchases and subscriptions for Android apps',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'key+prompt',
            dependencies: []
        },
        // OneSignal - Push notifications
        {
            id: 'onesignal',
            name: 'OneSignal',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/onesignal.png',
            description: 'Push notification service for mobile apps',
            connected: false,
            category: 'notification',
            comingSoon: true,
            connectionType: 'key',
            dependencies: []
        },
        // AWS S3 - Storage
        {
            id: 'aws-s3',
            name: 'AWS S3',
            logo: '/icons/integrations/aws.png',
            description: 'Cloud storage service by Amazon Web Services',
            connected: false,
            category: 'storage',
            comingSoon: true,
            connectionType: 'key',
            dependencies: []
        },
        // LemonSqueezy - Payment processing
        {
            id: 'lemonsqueezy',
            name: 'LemonSqueezy',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/lemonsqueezy.png',
            description: 'Payment processing and subscription management',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'key',
            dependencies: []
        },
        // Shopify - E-commerce
        {
            id: 'shopify',
            name: 'Shopify',
            logo: 'https://yrsdqwemtqgdwoixrrge.supabase.co/storage/v1/object/public/assets/icons/shopify.png',
            description: 'E-commerce platform for online stores',
            connected: false,
            category: 'payment',
            comingSoon: true,
            connectionType: 'oauth',
            dependencies: []
        }
    ];

    constructor(rootStore: RootStore) {
        this.rootStore = rootStore;
        makeAutoObservable(this);
    }
    
    // Check if all dependencies for an integration are satisfied
    isDependenciesSatisfied(integrationId: string): boolean {
        const integration = this.providers.find(p => p.id === integrationId);
        if (!integration || !integration.dependencies?.length) return true;
        
        return integration.dependencies.every(depId => {
            const dep = this.providers.find(p => p.id === depId);
            return dep?.connected === true;
        });
    }
    
    // Get all available integrations (not coming soon and dependencies satisfied)
    getAvailableIntegrations(): IntegrationProvider[] {
        return this.providers.filter(p => 
            !p.comingSoon && this.isDependenciesSatisfied(p.id)
        );
    }
    
    // Get the next setup step for an integration
    getNextSetupStep(integrationId: string): SetupStep | null {
        const integration = this.providers.find(p => p.id === integrationId);
        if (!integration || !integration.setupSteps?.length) return null;
        
        const progress = this.setupProgress.get(integrationId) || [];
        const incompleteStep = integration.setupSteps.find(step => 
            !progress.some(p => p.stepId === step.id && p.completed)
        );
        
        return incompleteStep || null;
    }
    
    // Mark a setup step as complete
    @action
    completeSetupStep(integrationId: string, stepId: string): void {
        const progress = this.setupProgress.get(integrationId) || [];
        const existingStep = progress.find(p => p.stepId === stepId);
        
        if (existingStep) {
            existingStep.completed = true;
        } else {
            progress.push({ stepId, completed: true });
        }
        
        this.setupProgress.set(integrationId, progress);
    }
    
    // Submit integration prompt
    async submitIntegrationPrompt(integrationId: string, promptData: any): Promise<void> {
        const integration = this.providers.find(p => p.id === integrationId);
        if (!integration) throw new Error(`Integration ${integrationId} not found`);
        
        // Here we would implement the logic to send the prompt to the AI
        // This is a placeholder for now
        console.log(`Submitting prompt for ${integrationId}:`, promptData);
        
        // For now, just mark as connected
        runInAction(() => {
            const index = this.providers.findIndex(p => p.id === integrationId);
            if (index >= 0) {
                this.providers[index].connected = true;
            }
        });
    }

    getConnection(provider: string) {
        return this.connections.get(provider);
    }

    getDatabases(provider: string) {
        return this.databases.get(provider) || [];
    }

    isLoadingConnection(provider: string) {
        return this.loadingConnections.has(provider);
    }

    isLoadingDatabases(provider: string) {
        return this.loadingDatabases.has(provider);
    }

    async fetchConnection(provider: string) {
        runInAction(() => this.loadingConnections.add(provider));
        try {
            const response = await fetch(`/api/integrations/${provider}?status=check`);
            if (!response.ok) {
                if (response.status === 404) {
                    runInAction(() => this.connections.delete(provider));
                    return null;
                }
                throw new Error('Failed to fetch connection');
            }
            const connection = await response.json();
            runInAction(() => this.connections.set(provider, connection));
            return connection;
        } catch (error) {
            console.error('Failed to fetch connection:', error);
            return null;
        } finally {
            runInAction(() => this.loadingConnections.delete(provider));
        }
    }

    async fetchDatabases(provider: string) {
        if (!this.getConnection(provider)) return;

        runInAction(() => this.loadingDatabases.add(provider));
        try {
            const response = await fetch(`/api/integrations/${provider}/databases`);
            if (!response.ok) throw new Error('Failed to fetch databases');
            const databases = await response.json();
            runInAction(() => this.databases.set(provider, databases));
            return databases;
        } catch (error) {
            console.error('Failed to fetch databases:', error);
            return [];
        } finally {
            runInAction(() => this.loadingDatabases.delete(provider));
        }
    }

    async disconnect(provider: string) {
        try {
            const response = await fetch(`/api/integrations/${provider}`, {
                method: 'DELETE',
            });
            if (!response.ok) throw new Error('Failed to disconnect');
            runInAction(() => {
                this.connections.delete(provider);
                this.databases.delete(provider);
            });
        } catch (error) {
            console.error('Failed to disconnect:', error);
            throw error;
        }
    }

    getAuthUrl(provider: string) {
        return `/api/integrations/${provider}`;
    }

    getProjectLinkState(chatId: string): ProjectLinkState {
        return this.projectLinkState.get(chatId) || {
            isLinking: false,
            error: null,
            success: false,
        };
    }

    @action
    setShouldSendMessage(value: boolean) {
        this.shouldSendMessage = value;
    }

    @action
    resetCurrentSelectedProjectId() {
        this.currentSelectedProjectId = null;
    }

    @action
    setProviderConnectionStatus(provider: string, connected: boolean) {
        if(this.providers[provider]) {
            this.providers[provider].connected = connected;
        } else {
            console.warn(`Provider ${provider} does exist. Trying to set connection status.`)
        }
    }

    @action
    async linkProject(chatId: string, projectId: string) {
        this.currentSelectedProjectId = projectId;
        this.shouldSendMessage = true;
        // Let's figure out a way to basically send a message with the correct params

        // runInAction(() => {
        //     this.projectLinkState.set(chatId, {
        //         isLinking: true,
        //         error: null,
        //         success: false,
        //     });
        // });

        // try {
        //     const response = await fetch(`/api/chat/${chatId}/supabase`, {
        //         method: 'POST',
        //         headers: {'Content-Type': 'application/json'},
        //         body: JSON.stringify({projectRef: projectId}),
        //     });
        //
        //     if (!response.ok) {
        //         throw new Error('Failed to link project');
        //     }
        //
        //     runInAction(() => {
        //         this.projectLinkState.set(chatId, {
        //             isLinking: false,
        //             error: null,
        //             success: true,
        //         });
        //     });
        //
        //     return await response.json();
        // } catch (error) {
        //     const errorMessage = error instanceof Error ? error.message : 'Failed to link project';
        //     runInAction(() => {
        //         this.projectLinkState.set(chatId, {
        //             isLinking: false,
        //             error: errorMessage,
        //             success: false,
        //         });
        //     });
        //     throw error;
        // }
    }
}

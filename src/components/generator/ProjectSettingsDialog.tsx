import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { XIcon, AlertCircle, ExternalLink, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { SettingsForm } from '@/components/settings/SettingsForm';
import { Project } from '@/lib/db/schema';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import MagicallyLogo from '@/components/logo';
import { IntegrationProvider, SetupStep } from '@/stores/IntegrationStore';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import { IntegrationDialog } from './IntegrationDialog';

// Define integration types based on IntegrationProvider
interface Integration {
  id: string;
  name: string;
  logo: string;
  description: string;
  connected: boolean;
  category: 'payment' | 'analytics' | 'auth' | 'database' | 'notification' | 'storage' | 'api';
  comingSoon: boolean;
}

interface ProjectSettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectId?: string;
  chatId?: string;
}

export const ProjectSettingsDialog = observer(({
  isOpen,
  onClose,
  projectId,
  chatId
}: ProjectSettingsDialogProps) => {
  const { generatorStore, integrationStore } = useStores();
  const [activeTab, setActiveTab] = useState<'settings' | 'integrations'>('settings');
  const [projectData, setProjectData] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationProvider | null>(null);
  const [showSetupSteps, setShowSetupSteps] = useState(false);
  const [activeIntegrationDialog, setActiveIntegrationDialog] = useState<string | null>(null);
  const [dialogMaxWidth, setDialogMaxWidth] = useState('95vw');

  // Fetch project data when component mounts or projectId changes
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (projectId) {
        const data = generatorStore.getProjectData(projectId);
        setProjectData(data);
      }
      setIsLoading(false);
    };

    fetchData();
  }, [projectId, generatorStore]);

  // Handle responsive dialog width
  useEffect(() => {
    const updateDialogWidth = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth >= 1024) {
          setDialogMaxWidth('80vw');
        } else if (window.innerWidth >= 640) {
          setDialogMaxWidth('800px');
        } else {
          setDialogMaxWidth('95vw');
        }
      }
    };

    updateDialogWidth();
    window.addEventListener('resize', updateDialogWidth);
    return () => window.removeEventListener('resize', updateDialogWidth);
  }, []);

  // Helper functions for integrations
  const checkDependencies = (integration: IntegrationProvider): boolean => {
    return integrationStore.isDependenciesSatisfied(integration.id);
  };

  // Check if an integration is actually connected based on project data
  const isIntegrationConnected = (integration: IntegrationProvider): boolean => {
    if (integration.id === 'supabase') {
      // For Supabase, check if project has connectionId (same as Header component)
      return !!projectData?.connectionId;
    }
    // For other integrations, fall back to the store's connected status
    return integration.connected;
  };
  
  const handleConnectClick = (integration: IntegrationProvider) => {
    setSelectedIntegration(integration);

    // Check if this integration has setup steps
    if (integration.setupSteps && integration.setupSteps.length > 0) {
      setShowSetupSteps(true);
    } else {
      // Handle different connection types
      if (integration.connectionType === 'oauth' && integration.id === 'supabase') {
        // Open the IntegrationDialog for OAuth integrations like Supabase
        setActiveIntegrationDialog(integration.id);
      } else {
        // For other types, show info for now
        toast.info(`Opening connection dialog for ${integration.name}`);
        // TODO: Implement other connection types (API key, prompt, etc.)
      }
    }
  };
  
  const handleManageClick = (integration: IntegrationProvider) => {
    setSelectedIntegration(integration);

    // Handle different integration types for management
    if (integration.connectionType === 'oauth' && integration.id === 'supabase') {
      // Open the IntegrationDialog for OAuth integrations like Supabase
      setActiveIntegrationDialog(integration.id);
    } else {
      // For other types, show info for now
      toast.info(`Managing ${integration.name} integration`);
      // TODO: Implement management UI for other integration types
    }
  };
  
  const completeSetupStep = (integrationId: string, stepId: string) => {
    integrationStore.completeSetupStep(integrationId, stepId);
    toast.success('Setup step completed');
  };
  
  const submitPrompt = async (integration: IntegrationProvider, promptData: any) => {
    try {
      await integrationStore.submitIntegrationPrompt(integration.id, promptData);
      toast.success(`${integration.name} configuration submitted`);
      setShowSetupSteps(false);
      setSelectedIntegration(null);
    } catch (error) {
      toast.error(`Error configuring ${integration.name}: ${error}`);
    }
  };
  
  // Get integrations from the store
  const integrations = integrationStore.providers;

  // Group integrations by category and sort by availability
  const groupedIntegrations = integrations.reduce((acc, integration) => {
    const category = integration.category || 'other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(integration);
    return acc;
  }, {} as Record<string, IntegrationProvider[]>);

  // Sort each category to show connected integrations first, then available (dependencies satisfied), then coming soon
  Object.keys(groupedIntegrations).forEach(category => {
    groupedIntegrations[category].sort((a, b) => {
      // Connected integrations first
      const aConnected = isIntegrationConnected(a);
      const bConnected = isIntegrationConnected(b);
      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      // Then available integrations (dependencies satisfied)
      const aDepsOk = checkDependencies(a);
      const bDepsOk = checkDependencies(b);
      if (aDepsOk && !bDepsOk) return -1;
      if (!aDepsOk && bDepsOk) return 1;

      // Then by coming soon status
      if (!a.comingSoon && b.comingSoon) return -1;
      if (a.comingSoon && !b.comingSoon) return 1;

      return 0;
    });
  });

  const categoryNames: Record<string, string> = {
    'payment': 'Payment',
    'analytics': 'Analytics',
    'auth': 'Authentication',
    'database': 'Database',
    'notification': 'Notifications',
    'storage': 'Storage',
    'api': 'API',
    'other': 'Other'
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="h-[85vh] p-0 bg-background/95 backdrop-blur-xl border-border/50 overflow-hidden flex flex-col"
        style={{
          maxWidth: dialogMaxWidth,
        }}
      >
        <div className="relative flex flex-col h-full">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-chart-3/5" />

          <div className="relative z-10 flex flex-col h-full">
            {/* Header */}
            <div className="px-4 sm:px-6 py-3 border-b border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <MagicallyLogo iconOnly className="h-8 w-8" />
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Project Settings</h2>
                    <p className="text-sm text-muted-foreground">
                      Manage your project settings and integrations
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8 rounded-full"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Content with sidebar navigation */}
            <div className="flex flex-1 overflow-hidden">
              {/* Sidebar Navigation */}
              <div className="w-48 border-r border-border/50 bg-background/30 backdrop-blur-sm flex-shrink-0">
                <div className="p-2">
                  <nav className="space-y-1">
                    <button
                      onClick={() => setActiveTab('settings')}
                      className={cn(
                        'w-full text-left px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2',
                        activeTab === 'settings' 
                          ? 'bg-primary/10 text-primary font-medium'
                          : 'hover:bg-secondary/50 text-foreground'
                      )}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-settings">
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                      <span>Project Settings</span>
                    </button>
                    <button
                      onClick={() => setActiveTab('integrations')}
                      className={cn(
                        'w-full text-left px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2',
                        activeTab === 'integrations' 
                          ? 'bg-primary/10 text-primary font-medium'
                          : 'hover:bg-secondary/50 text-foreground'
                      )}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-puzzle">
                        <path d="M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z"></path>
                      </svg>
                      <span>Integrations</span>
                    </button>
                  </nav>
                </div>
              </div>

              {/* Main Content Area */}
              <div className="flex-1 overflow-y-auto h-full">
                <AnimatePresence mode="wait">
                  {activeTab === 'settings' && (
                    <motion.div
                      key="settings"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="p-4 sm:p-6"
                    >
                      {projectData && (
                        <SettingsForm 
                          projectId={projectId || ''} 
                          initialData={projectData} 
                          isLoading={isLoading}
                        />
                      )}
                      {!projectData && !isLoading && (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground">Project data not found</p>
                        </div>
                      )}
                      {!projectData && isLoading && (
                        <div className="text-center py-8">
                          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                          <p className="text-muted-foreground">Loading project settings...</p>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {activeTab === 'integrations' && (
                    <motion.div
                      key="integrations"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="p-4 sm:p-6"
                    >
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-medium mb-4">Available Integrations</h3>
                          <p className="text-sm text-muted-foreground mb-6">
                            Connect your app with these services to enhance its functionality
                          </p>
                        </div>

                        {Object.entries(groupedIntegrations).map(([category, items]) => (
                          <div key={category} className="space-y-3">
                            <h4 className="text-sm font-medium text-muted-foreground">{categoryNames[category]}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {items.map(integration => {
                                const isAvailable = !integration.comingSoon;
                                const dependenciesMet = checkDependencies(integration);
                                const canConnect = isAvailable && dependenciesMet;
                                const hasSetupSteps = integration.setupSteps && integration.setupSteps.length > 0;
                                const connected = isIntegrationConnected(integration);
                                
                                return (
                                  <motion.div
                                    key={integration.id}
                                    whileHover={{ scale: canConnect ? 1.01 : 1 }}
                                    className={cn(
                                      'p-4 rounded-xl border-2 transition-all duration-300',
                                      'bg-gradient-to-br backdrop-blur-sm',
                                      connected
                                        ? 'border-primary/50 bg-primary/5 cursor-pointer'
                                        : canConnect
                                          ? 'border-border/50 hover:border-primary/30 bg-secondary/30 cursor-pointer'
                                          : 'border-border/30 bg-secondary/20 opacity-80'
                                    )}
                                  >
                                    <div className="flex items-start gap-3">
                                      <div className="h-10 w-10 rounded-lg bg-white flex items-center justify-center flex-shrink-0 overflow-hidden border border-border/50">
                                        {integration.logo ? (
                                          <img 
                                            src={integration.logo} 
                                            alt={integration.name} 
                                            className="h-8 w-8 object-contain"
                                          />
                                        ) : (
                                          <div className="h-8 w-8 rounded-md bg-primary/20 flex items-center justify-center">
                                            <span className="text-xs font-medium">{integration.name.substring(0, 2)}</span>
                                          </div>
                                        )}
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-1">
                                          <h4 className="font-semibold text-foreground">{integration.name}</h4>
                                          {connected && (
                                            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30 ml-1">
                                              <Check className="h-3 w-3 mr-1" /> Connected
                                            </Badge>
                                          )}
                                        </div>
                                        
                                        <p className="text-xs text-muted-foreground mb-2">
                                          {integration.description}
                                        </p>
                                        
                                        {/* Status badges */}
                                        <div className="flex flex-wrap gap-1 mb-2">
                                          {integration.comingSoon && (
                                            <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/30">
                                              Coming Soon
                                            </Badge>
                                          )}
                                          
                                          {!integration.comingSoon && !dependenciesMet && integration.dependencies && integration.dependencies.length > 0 && (
                                            <TooltipProvider>
                                              <Tooltip>
                                                <TooltipTrigger asChild>
                                                  <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/30 cursor-help">
                                                    <AlertCircle className="h-3 w-3 mr-1" /> Dependencies Required
                                                  </Badge>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                  <p className="text-xs">This integration requires:</p>
                                                  <ul className="text-xs list-disc pl-4 mt-1">
                                                    {integration.dependencies.map(depId => {
                                                      const dep = integrations.find(p => p.id === depId);
                                                      return (
                                                        <li key={depId}>{dep?.name || depId}</li>
                                                      );
                                                    })}
                                                  </ul>
                                                </TooltipContent>
                                              </Tooltip>
                                            </TooltipProvider>
                                          )}
                                          
                                          {integration.connectionType && (
                                            <div className="flex gap-1">
                                              {integration.connectionType.includes('oauth') && (
                                                <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/30">
                                                  OAuth
                                                </Badge>
                                              )}
                                              {integration.connectionType.includes('key') && (
                                                <Badge variant="outline" className="bg-purple-500/10 text-purple-500 border-purple-500/30">
                                                  API Key
                                                </Badge>
                                              )}
                                              {integration.connectionType.includes('prompt') && (
                                                <Badge variant="outline" className="bg-indigo-500/10 text-indigo-500 border-indigo-500/30">
                                                  AI Config
                                                </Badge>
                                              )}
                                            </div>
                                          )}
                                        </div>
                                        
                                        {/* Action buttons */}
                                        {connected ? (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-xs h-7 w-full"
                                            onClick={() => handleManageClick(integration)}
                                          >
                                            Manage
                                          </Button>
                                        ) : isAvailable && !integration.comingSoon ? (
                                          <div className="space-y-1">
                                            <Button 
                                              variant="outline" 
                                              size="sm"
                                              className="text-xs h-7 w-full"
                                              disabled={!dependenciesMet}
                                              onClick={() => dependenciesMet && handleConnectClick(integration)}
                                            >
                                              {!dependenciesMet ? 'Locked' : 'Connect'}
                                            </Button>
                                            
                                            {!dependenciesMet && integration.dependencies && (
                                              <p className="text-[10px] text-muted-foreground">
                                                Connect required dependencies first
                                              </p>
                                            )}
                                          </div>
                                        ) : (
                                          <div className="flex items-center gap-1 w-full">
                                            <span className="text-xs text-amber-500 font-medium">Coming Soon</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
                
                {/* Setup Steps UI */}
                <AnimatePresence>
                  {showSetupSteps && selectedIntegration && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 20 }}
                      transition={{ duration: 0.2 }}
                      className="absolute inset-0 bg-background/95 backdrop-blur-xl p-6 flex flex-col"
                    >
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-lg bg-white flex items-center justify-center overflow-hidden border border-border/50">
                            {selectedIntegration.logo ? (
                              <img 
                                src={selectedIntegration.logo} 
                                alt={selectedIntegration.name} 
                                className="h-8 w-8 object-contain"
                              />
                            ) : (
                              <div className="h-8 w-8 rounded-md bg-primary/20 flex items-center justify-center">
                                <span className="text-xs font-medium">{selectedIntegration.name.substring(0, 2)}</span>
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold">{selectedIntegration.name} Setup</h3>
                            <p className="text-sm text-muted-foreground">Follow these steps to complete the integration</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setShowSetupSteps(false);
                            setSelectedIntegration(null);
                          }}
                        >
                          <XIcon className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex-1 overflow-y-auto">
                        {selectedIntegration.setupSteps && selectedIntegration.setupSteps.map((step, index) => {
                          const isCompleted = step.completed;
                          const isActive = !isCompleted && (!index || (index > 0 && selectedIntegration.setupSteps?.[index - 1].completed));
                          
                          return (
                            <div 
                              key={step.id}
                              className={cn(
                                'mb-4 p-4 rounded-lg border-2 transition-all',
                                isCompleted 
                                  ? 'border-primary/30 bg-primary/5' 
                                  : isActive
                                    ? 'border-blue-500/30 bg-blue-500/5'
                                    : 'border-border/30 bg-secondary/10 opacity-60'
                              )}
                            >
                              <div className="flex items-start gap-3">
                                <div className={cn(
                                  'h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0',
                                  isCompleted 
                                    ? 'bg-primary text-primary-foreground' 
                                    : isActive
                                      ? 'bg-blue-500 text-white'
                                      : 'bg-muted text-muted-foreground'
                                )}>
                                  {isCompleted ? (
                                    <Check className="h-3 w-3" />
                                  ) : (
                                    <span className="text-xs font-medium">{index + 1}</span>
                                  )}
                                </div>
                                <div className="flex-1">
                                  <h4 className="font-medium mb-1">{step.title}</h4>
                                  <p className="text-sm text-muted-foreground mb-3">{step.description}</p>
                                  
                                  {isActive && (
                                    <div className="space-y-3">
                                      {step.externalUrl && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="gap-1"
                                          onClick={() => window.open(step.externalUrl, '_blank')}
                                        >
                                          <ExternalLink className="h-3 w-3" />
                                          Open Setup Guide
                                        </Button>
                                      )}
                                      
                                      <Button
                                        variant="default"
                                        size="sm"
                                        onClick={() => completeSetupStep(selectedIntegration.id, step.id)}
                                      >
                                        {step.buttonText || 'Complete Step'}
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                        
                        {/* Final configuration prompt if needed */}
                        {selectedIntegration.promptTemplate && (
                          <div className="mt-6 p-4 rounded-lg border-2 border-primary/30 bg-primary/5">
                            <h4 className="font-medium mb-2">Additional Configuration</h4>
                            <p className="text-sm text-muted-foreground mb-4">
                              Provide any additional details needed to configure this integration
                            </p>
                            
                            <div className="space-y-3">
                              <textarea 
                                className="w-full h-24 p-3 rounded-md border border-border bg-background/50"
                                placeholder={selectedIntegration.promptTemplate}
                                id="integration-prompt"
                              />
                              
                              <Button
                                variant="default"
                                onClick={() => {
                                  const promptData = (document.getElementById('integration-prompt') as HTMLTextAreaElement)?.value;
                                  if (promptData) {
                                    submitPrompt(selectedIntegration, promptData);
                                  } else {
                                    toast.error('Please enter configuration details');
                                  }
                                }}
                              >
                                Submit Configuration
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Integration Dialog for OAuth connections */}
      {activeIntegrationDialog && (
        <IntegrationDialog
          providerId={activeIntegrationDialog}
          open={!!activeIntegrationDialog}
          chatId={chatId}
          onOpenChange={(open) => {
            if (!open) {
              setActiveIntegrationDialog(null);
              // Close the main settings dialog when integration is successfully connected
              // This will be triggered when the integration dialog closes after successful connection
              onClose();
            }
          }}
        />
      )}
    </Dialog>
  );
});

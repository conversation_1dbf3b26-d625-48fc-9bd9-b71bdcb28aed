'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  Database, 
  Zap, 
  ArrowRight, 
  X,
  CheckCircle2,
  Clock,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectSettingsDialog } from '@/components/generator/ProjectSettingsDialog';

interface IntegrationCalloutProps {
  projectId: string;
  chatId: string;
}

export const IntegrationCallout = observer(({ projectId, chatId }: IntegrationCalloutProps) => {
  const { generatorStore } = useStores();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  
  // Get project data to check Supabase connection
  const projectData = generatorStore.getProjectData(projectId);
  const isSupabaseConnected = !!projectData?.connectionId;
  
  // Don't show if Supabase is already connected or if dismissed
  if (isSupabaseConnected || isDismissed) {
    return null;
  }

  const benefits = [
    {
      icon: Database,
      title: "Real-time Database",
      description: "Instant data sync across all devices"
    },
    {
      icon: Shield,
      title: "Built-in Auth",
      description: "Secure user authentication out of the box"
    },
    {
      icon: Zap,
      title: "Edge Functions",
      description: "Serverless functions at the edge"
    }
  ];

  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.98 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.98 }}
          transition={{ 
            duration: 0.4, 
            ease: [0.16, 1, 0.3, 1],
            delay: 0.2 
          }}
          className="mt-4 relative"
        >
          {/* Glassmorphic container with Apple-style design */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 via-white/60 to-white/40 dark:from-white/10 dark:via-white/5 dark:to-white/2 backdrop-blur-xl border border-white/20 dark:border-white/10 shadow-2xl shadow-black/5 dark:shadow-black/20">
            
            {/* Animated background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/5 to-pink-500/10 dark:from-blue-400/5 dark:via-purple-400/3 dark:to-pink-400/5" />
            
            {/* Subtle animated orbs */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse delay-1000" />
            
            {/* Dismiss button */}
            <button
              onClick={() => setIsDismissed(true)}
              className="absolute top-3 right-3 z-10 p-1.5 rounded-full bg-black/5 dark:bg-white/10 hover:bg-black/10 dark:hover:bg-white/20 transition-colors duration-200"
            >
              <X className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />
            </button>
            
            {/* Content */}
            <div className="relative z-10 p-6">
              {/* Header */}
              <div className="flex items-center gap-3 mb-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                  <Sparkles className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Supercharge Your App
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Connect Supabase for instant backend power
                  </p>
                </div>
                <Badge 
                  variant="secondary" 
                  className="ml-auto bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-700 dark:text-blue-300 border-blue-200/50 dark:border-blue-400/30"
                >
                  <Clock className="h-3 w-3 mr-1" />
                  2 min setup
                </Badge>
              </div>

              {/* Benefits grid */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-5">
                {benefits.map((benefit, index) => (
                  <motion.div
                    key={benefit.title}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="flex items-start gap-3 p-3 rounded-xl bg-white/40 dark:bg-white/5 border border-white/30 dark:border-white/10"
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                      <benefit.icon className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                        {benefit.title}
                      </h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* CTA Button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Button
                  onClick={() => setIsSettingsOpen(true)}
                  className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group"
                >
                  <span>Connect Supabase</span>
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-0.5 transition-transform duration-200" />
                </Button>
              </motion.div>

              {/* Trust indicators */}
              <div className="flex items-center justify-center gap-4 mt-4 pt-4 border-t border-white/20 dark:border-white/10">
                <div className="flex items-center gap-1.5 text-xs text-gray-600 dark:text-gray-400">
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                  <span>Free tier available</span>
                </div>
                <div className="flex items-center gap-1.5 text-xs text-gray-600 dark:text-gray-400">
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                  <span>No credit card required</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Project Settings Dialog */}
      <ProjectSettingsDialog
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        projectId={projectId}
        chatId={chatId}
        defaultTab="integrations"
      />
    </>
  );
});

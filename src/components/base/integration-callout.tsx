'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectSettingsDialog } from '@/components/generator/ProjectSettingsDialog';

interface IntegrationCalloutProps {
  projectId: string;
  chatId: string;
}

export const IntegrationCallout = observer(({ projectId, chatId }: IntegrationCalloutProps) => {
  const { generatorStore, integrationStore } = useStores();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Get project data to check Supabase connection
  const projectData = generatorStore.getProjectData(projectId);
  const isSupabaseConnected = !!projectData?.connectionId;

  // Don't show if Supabase is already connected or if dismissed
  if (isSupabaseConnected || isDismissed) {
    return null;
  }

  // Get available integrations for logos
  const availableIntegrations = integrationStore.providers.filter(p => !p.comingSoon).slice(0, 4);

  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -8 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="mt-3"
        >
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-50/80 to-purple-50/80 dark:from-blue-950/20 dark:to-purple-950/20 backdrop-blur-sm border border-blue-200/30 dark:border-blue-800/30 p-4">

            {/* Dismiss button */}
            <button
              onClick={() => setIsDismissed(true)}
              className="absolute top-2 right-2 p-1 rounded-full hover:bg-black/5 dark:hover:bg-white/10 transition-colors duration-200"
            >
              <X className="h-3 w-3 text-gray-500 dark:text-gray-400" />
            </button>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    Connect integrations
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Add backend services to your app
                  </p>
                </div>

                {/* Overlapped integration logos */}
                <div className="flex -space-x-2">
                  {availableIntegrations.map((integration, index) => (
                    <div
                      key={integration.id}
                      className={cn(
                        "relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full border-2 border-white dark:border-gray-800 bg-white",
                        index > 0 && "ml-[-8px]"
                      )}
                      style={{ zIndex: availableIntegrations.length - index }}
                    >
                      {integration.logo ? (
                        <img
                          src={integration.logo}
                          alt={integration.name}
                          className="h-full w-full object-contain p-1"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                            {integration.name.substring(0, 2)}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <Button
                onClick={() => setIsSettingsOpen(true)}
                size="sm"
                className="h-8 px-3 bg-blue-600 hover:bg-blue-700 text-white text-xs group"
              >
                <span>Connect</span>
                <ArrowRight className="h-3 w-3 ml-1 group-hover:translate-x-0.5 transition-transform duration-200" />
              </Button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Project Settings Dialog */}
      <ProjectSettingsDialog
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        projectId={projectId}
        chatId={chatId}
        defaultTab="integrations"
      />
    </>
  );
});

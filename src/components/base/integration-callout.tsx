'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectSettingsDialog } from '@/components/generator/ProjectSettingsDialog';

interface IntegrationCalloutProps {
  projectId: string;
  chatId: string;
}

export const IntegrationCallout = observer(({ projectId, chatId }: IntegrationCalloutProps) => {
  const { generatorStore, integrationStore } = useStores();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Get project data to check Supabase connection
  const projectData = generatorStore.getProjectData(projectId);
  const isSupabaseConnected = !!projectData?.connectionId;

  // Don't show if Supabase is already connected
  if (isSupabaseConnected) {
    return null;
  }

  // Get diverse integrations for logos - pick one from each category
  const availableIntegrations = (() => {
    const allProviders = integrationStore.providers.filter(p => !p.comingSoon);
    const categories = ['database', 'auth', 'payment', 'analytics'];
    const diverse = [];

    // Get one integration per category
    for (const category of categories) {
      const provider = allProviders.find(p => p.category === category);
      if (provider) diverse.push(provider);
    }

    return diverse.slice(0, 4);
  })();

  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -8 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="mt-3"
        >
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-accent/10 to-accent/5 dark:from-accent/20 dark:to-accent/10 backdrop-blur-sm border border-accent/20 dark:border-accent/30 p-4">

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    Your app needs a boost
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Connect backend, auth and make it come alive
                  </p>
                </div>

                {/* Overlapped integration logos */}
                <div className="flex -space-x-2">
                  {availableIntegrations.map((integration, index) => (
                    <div
                      key={integration.id}
                      className={cn(
                        "relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full border-2 border-white dark:border-gray-800 bg-white",
                        index > 0 && "ml-[-8px]"
                      )}
                      style={{ zIndex: availableIntegrations.length - index }}
                    >
                      {integration.logo ? (
                        <img
                          src={integration.logo}
                          alt={integration.name}
                          className="h-full w-full object-contain p-1"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-100 dark:bg-gray-700">
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                            {integration.name.substring(0, 2)}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <Button
                onClick={() => setIsSettingsOpen(true)}
                size="sm"
                className="h-8 px-3 bg-accent hover:bg-accent/90 text-accent-foreground text-xs group"
              >
                <span>Connect</span>
                <ArrowRight className="h-3 w-3 ml-1 group-hover:translate-x-0.5 transition-transform duration-200" />
              </Button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Project Settings Dialog */}
      <ProjectSettingsDialog
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        projectId={projectId}
        chatId={chatId}
        defaultTab="integrations"
      />
    </>
  );
});
